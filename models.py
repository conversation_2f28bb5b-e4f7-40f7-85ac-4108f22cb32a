from database import get_db_connection
import sqlite3

class Student:
    def __init__(self, id=None, name=None, email=None, age=None, course=None, created_at=None):
        self.id = id
        self.name = name
        self.email = email
        self.age = age
        self.course = course
        self.created_at = created_at

    @staticmethod
    def get_all():
        """Get all students from database"""
        conn = get_db_connection()
        students = conn.execute('SELECT * FROM students ORDER BY name').fetchall()
        conn.close()
        return students

    @staticmethod
    def get_by_id(student_id):
        """Get student by ID"""
        conn = get_db_connection()
        student = conn.execute('SELECT * FROM students WHERE id = ?', (student_id,)).fetchone()
        conn.close()
        return student

    @staticmethod
    def create(name, email, age, course):
        """Create new student"""
        conn = get_db_connection()
        try:
            conn.execute(
                'INSERT INTO students (name, email, age, course) VALUES (?, ?, ?, ?)',
                (name, email, age, course)
            )
            conn.commit()
            conn.close()
            return True
        except sqlite3.IntegrityError:
            conn.close()
            return False

    @staticmethod
    def update(student_id, name, email, age, course):
        """Update existing student"""
        conn = get_db_connection()
        try:
            conn.execute(
                'UPDATE students SET name = ?, email = ?, age = ?, course = ? WHERE id = ?',
                (name, email, age, course, student_id)
            )
            conn.commit()
            conn.close()
            return True
        except sqlite3.IntegrityError:
            conn.close()
            return False

    @staticmethod
    def delete(student_id):
        """Delete student by ID"""
        conn = get_db_connection()
        conn.execute('DELETE FROM students WHERE id = ?', (student_id,))
        conn.commit()
        conn.close()
        return True
