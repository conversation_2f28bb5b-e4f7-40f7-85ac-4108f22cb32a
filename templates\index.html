{% extends "base.html" %}

{% block title %}All Students - Student Management System{% endblock %}

{% block content %}
<h2>All Students</h2>

{% if students %}
    <table>
        <thead>
            <tr>
                <th>ID</th>
                <th>Name</th>
                <th>Email</th>
                <th>Age</th>
                <th>Course</th>
                <th>Created At</th>
                <th>Actions</th>
            </tr>
        </thead>
        <tbody>
            {% for student in students %}
            <tr>
                <td>{{ student.id }}</td>
                <td>{{ student.name }}</td>
                <td>{{ student.email }}</td>
                <td>{{ student.age }}</td>
                <td>{{ student.course }}</td>
                <td>{{ student.created_at }}</td>
                <td>
                    <a href="/edit/{{ student.id }}" class="btn btn-primary">Edit</a>
                    <a href="/delete/{{ student.id }}" class="btn btn-danger" 
                       onclick="return confirm('Are you sure you want to delete this student?')">Delete</a>
                </td>
            </tr>
            {% endfor %}
        </tbody>
    </table>
{% else %}
    <p>No students found. <a href="/add">Add the first student</a></p>
{% endif %}
{% endblock %}
