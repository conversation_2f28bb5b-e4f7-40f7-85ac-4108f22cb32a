{% extends "base.html" %}

{% block title %}Edit Student - Student Management System{% endblock %}

{% block content %}
<h2>Edit Student</h2>

{% if error %}
    <div class="alert alert-danger">{{ error }}</div>
{% endif %}

<form method="POST">
    <div class="form-group">
        <label for="name">Name:</label>
        <input type="text" id="name" name="name" required value="{{ student.name }}">
    </div>
    
    <div class="form-group">
        <label for="email">Email:</label>
        <input type="email" id="email" name="email" required value="{{ student.email }}">
    </div>
    
    <div class="form-group">
        <label for="age">Age:</label>
        <input type="number" id="age" name="age" min="1" max="120" required value="{{ student.age }}">
    </div>
    
    <div class="form-group">
        <label for="course">Course:</label>
        <input type="text" id="course" name="course" required value="{{ student.course }}">
    </div>
    
    <div class="form-group">
        <button type="submit" class="btn btn-success">Update Student</button>
        <a href="/" class="btn btn-secondary">Cancel</a>
    </div>
</form>
{% endblock %}
