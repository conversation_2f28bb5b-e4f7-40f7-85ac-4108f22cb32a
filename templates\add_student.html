{% extends "base.html" %}

{% block title %}Add Student - Student Management System{% endblock %}

{% block content %}
<h2>Add New Student</h2>

{% if error %}
    <div class="alert alert-danger">{{ error }}</div>
{% endif %}

<form method="POST">
    <div class="form-group">
        <label for="name">Name:</label>
        <input type="text" id="name" name="name" required value="{{ request.form.name or '' }}">
    </div>
    
    <div class="form-group">
        <label for="email">Email:</label>
        <input type="email" id="email" name="email" required value="{{ request.form.email or '' }}">
    </div>
    
    <div class="form-group">
        <label for="age">Age:</label>
        <input type="number" id="age" name="age" min="1" max="120" required value="{{ request.form.age or '' }}">
    </div>
    
    <div class="form-group">
        <label for="course">Course:</label>
        <input type="text" id="course" name="course" required value="{{ request.form.course or '' }}">
    </div>
    
    <div class="form-group">
        <button type="submit" class="btn btn-success">Add Student</button>
        <a href="/" class="btn btn-secondary">Cancel</a>
    </div>
</form>
{% endblock %}
