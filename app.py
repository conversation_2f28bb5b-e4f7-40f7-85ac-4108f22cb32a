from flask import Flask, render_template, request, redirect, url_for, flash, make_response
from database import init_db
from models import Student
from reportlab.lib.pagesizes import letter
from reportlab.platypus import SimpleDocTemplate, Table, TableStyle, Paragraph, Spacer
from reportlab.lib.styles import getSampleStyleSheet
from reportlab.lib import colors
from reportlab.lib.units import inch
import io
import sqlite3

app = Flask(__name__)
app.secret_key = 'your-secret-key-change-this'

# Initialize database on startup
with app.app_context():
    init_db()

@app.route('/')
def index():
    """Display all students"""
    students = Student.get_all()
    return render_template('index.html', students=students)

@app.route('/add', methods=['GET', 'POST'])
def add_student():
    """Add new student"""
    if request.method == 'POST':
        name = request.form['name'].strip()
        email = request.form['email'].strip()
        age = request.form['age']
        course = request.form['course'].strip()

        # Validation
        if not name or not email or not age or not course:
            return render_template('add_student.html', error='All fields are required')

        try:
            age = int(age)
            if age < 1 or age > 120:
                return render_template('add_student.html', error='Age must be between 1 and 120')
        except ValueError:
            return render_template('add_student.html', error='Age must be a valid number')

        # Create student
        if Student.create(name, email, age, course):
            flash('Student added successfully!')
            return redirect(url_for('index'))
        else:
            return render_template('add_student.html', error='Email already exists')

    return render_template('add_student.html')

@app.route('/edit/<int:student_id>', methods=['GET', 'POST'])
def edit_student(student_id):
    """Edit existing student"""
    student = Student.get_by_id(student_id)
    if not student:
        flash('Student not found')
        return redirect(url_for('index'))

    if request.method == 'POST':
        name = request.form['name'].strip()
        email = request.form['email'].strip()
        age = request.form['age']
        course = request.form['course'].strip()

        # Validation
        if not name or not email or not age or not course:
            return render_template('edit_student.html', student=student, error='All fields are required')

        try:
            age = int(age)
            if age < 1 or age > 120:
                return render_template('edit_student.html', student=student, error='Age must be between 1 and 120')
        except ValueError:
            return render_template('edit_student.html', student=student, error='Age must be a valid number')

        # Update student
        if Student.update(student_id, name, email, age, course):
            flash('Student updated successfully!')
            return redirect(url_for('index'))
        else:
            return render_template('edit_student.html', student=student, error='Email already exists')

    return render_template('edit_student.html', student=student)

@app.route('/delete/<int:student_id>')
def delete_student(student_id):
    """Delete student"""
    student = Student.get_by_id(student_id)
    if student:
        Student.delete(student_id)
        flash('Student deleted successfully!')
    else:
        flash('Student not found')

    return redirect(url_for('index'))

@app.route('/report')
def generate_pdf_report():
    """Generate PDF report of all students"""
    students = Student.get_all()

    # Create PDF in memory
    buffer = io.BytesIO()
    doc = SimpleDocTemplate(buffer, pagesize=letter)

    # Get styles
    styles = getSampleStyleSheet()

    # Create content
    content = []

    # Title
    title = Paragraph("Student Management System Report", styles['Title'])
    content.append(title)
    content.append(Spacer(1, 20))

    if students:
        # Create table data
        data = [['ID', 'Name', 'Email', 'Age', 'Course', 'Created At']]

        for student in students:
            data.append([
                str(student['id']),
                student['name'],
                student['email'],
                str(student['age']),
                student['course'],
                student['created_at']
            ])

        # Create table
        table = Table(data, colWidths=[0.5*inch, 1.5*inch, 2*inch, 0.7*inch, 1.5*inch, 1.3*inch])

        # Style the table
        table.setStyle(TableStyle([
            ('BACKGROUND', (0, 0), (-1, 0), colors.grey),
            ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
            ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
            ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
            ('FONTSIZE', (0, 0), (-1, 0), 10),
            ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
            ('BACKGROUND', (0, 1), (-1, -1), colors.beige),
            ('FONTNAME', (0, 1), (-1, -1), 'Helvetica'),
            ('FONTSIZE', (0, 1), (-1, -1), 8),
            ('GRID', (0, 0), (-1, -1), 1, colors.black)
        ]))

        content.append(table)
    else:
        no_data = Paragraph("No students found in the database.", styles['Normal'])
        content.append(no_data)

    # Build PDF
    doc.build(content)

    # Get PDF data
    pdf_data = buffer.getvalue()
    buffer.close()

    # Create response
    response = make_response(pdf_data)
    response.headers['Content-Type'] = 'application/pdf'
    response.headers['Content-Disposition'] = 'attachment; filename=students_report.pdf'

    return response

if __name__ == '__main__':
    app.run(debug=True)
